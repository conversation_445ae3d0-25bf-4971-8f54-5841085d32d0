/* 
/* Hero Section with Background Image */
html,body{
    overflow-x: hidden;

    width: 100%;
}
/* hero-section */
.hero {
  background: url('../images/banner-bg.png') no-repeat center;
  background-size: cover;
}
.nav-item a {
    font-weight: 600;
    font-size: 15px;
}
.nav-item a:hover {
    color: #13499f!important;
    text-decoration: none;
}
.head{
    position: relative;
    z-index: 1;
   
}
.harness h1{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    color:white;
    font-size: 65px;
    font-weight: 700;
}
.harness p{
    color: white;
    text-align: justify;
    font-size: 20px;
    font-weight: 15px;

}
.mt-3{
    color: white;
}

/* welcome-section */
.welcome-section{
    padding: 50px 0px;
}
.welcome-img{
    position: relative;
}
.welcome-con{
    color:black;
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 42px;
    text-align: justify;

}
.welcome-description{
    color: rgb(59, 55, 55);
    text-align: justify;
}

.small-1{
    position: absolute;
    top: 13px;
    right: 62px;
}
.small-2{
    position: absolute;
    top: 280px;
    right: 60px;

}

/* features-section */
.features-section{
    padding: 50px 0px;
    background-color: #13499f;
}
.fh{
    font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
    font-weight: 700;
    font-size: 42px;
    color: white;
    text-align: center;
    margin-bottom: 30px;


}
.feature-card{
    padding: 30px;
    background-color: white;
    align-items: center;
    border-radius: 10px; 
    min-height: 490px;
    
}
.feature-card p{
    text-align: justify;
    font-size: 18;
    line-height: 1.6rem; 
}
.feature-card h3{
    text-align: justify;
    padding-top: 35px;
    font-size: 20px ;
    font-weight: 100px;
}

/* notification-section */
.notification-section{
    background-color: #bbebff;
    padding: 50px 0px;
}


.notify-text{
    margin-top: 140px;
}
.notify-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 40px;
font-weight: 700;
}

.notify-text p{
    text-align: justify;
    font-size: 18px;  

}
.list{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }

/* seamless-section */
.seamless-section{
    background-color: white;
    padding: 70px 0px;
}
.seamless-text{
    margin-top: 140px;
}
.seamless-text h3{
font-family: 'Poppins', 'Montserrat', 'Arial Black', sans-serif;
font-size: 42px;
font-weight: 700;
}
.seamless-text p{
    text-align: justify;
    font-size: 18px;  

}
.list-1{
    font-size: 20px;
    list-style-image:url(../images/icon.png) }

/*flexibility-section */
.flexibility-section{
    background-color: #13499f;
    height: 100vh;
}

/* Left side - Full image */
.full-image{
    width: 100%;
    height: 100vh;
    object-fit: cover;
}

/* Right side - Center accordion */
.accordion-center{
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flexibility-style{
    width: 80%;
    max-width: 500px;
}

.accordion-body{
    background-color:#13499f;
    color: white;
}

/* healthcare-section */
.healthcare-section{
    background-color: #beebfe;
    padding: 50px 0px;
}
.healthcare-text{
    margin-top: 140px;
  
}
.healthcare-text h3{
font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
font-size: 40px;
font-weight: 700;
}
.healthcare-text p{
    text-align: justify;
    font-size: 19px;
} 

/* empower-section */
.empower-section{
    background-color:#13499f;
    padding: 50px 60px;
}
.empower-section h1{
    padding: 40px 0px;
    color: white;
    font-size: 42px;
    font-weight: 700;
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
}
.empower-para{
    color: white;
    font-size: 18px;
    text-align: justify;
}
.empower-card{
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    margin-top: 50px;
    min-height: 280px;
    
}
.empower-card h4{
    font-size: 15px;
    font-weight: 10px;
    
}
.empower-card p{
    align-items:center;
    font-size: 16px;
    color: #39657D;
}

/* Form Container */
.form {
    background-color: #0c3c92;
    padding: 30px;
    border-radius: 10px;
}

/* Labels */
.form label {
    color: white;
    font-weight: 500;
    margin-bottom: 5px;
}

/* Input Fields */
.form .form-control {
    padding: 12px 15px;
    font-size: 18px;
    margin-bottom: 20px;
}
/* Submit Button */
.form .btn-primary {
    background-color: #4dd0ff;
    color: black;
    font-size: 20px;
    font-weight: 700;
    border-radius: 6px;
    padding: 12px;
    border: none;
    width: 100%;
}

/* key-feature-section */
.key-feature-section{
    background-color: white;
    padding: 50px 0px;
    text-align: center;
}
.key-feature-section h1{
    font-family: 'Poppins','Montserrat','Arial Black',sans-serif;
    font-size: 42px;
    font-weight: 700;  
    color: #0c3c92;
}
.key-feature-section p{
    font-size: 19px;
}
/* footer */
.footer{
    background-color: #eff6fe;
    padding: 55px 0px;
}
.footer-logo p{
    font-size: 18px;
}
.list-footer-Ql{
    list-style-image: url(../images/footer-icon.png);
}
.list-footer-Cu li.ad{
     list-style-image: url(../images/location-icon.png);
} 
.list-footer-Cu{
    list-style-image: url(../images/mail-icon.png);
}
.list-footer-Ql-2{
    list-style-image: url(../images/footer-icon.png);
}
.footer-bottom {
    width: 100vw;
    background: #005cb9;
    color: #fff;
    padding: 20px 0;
    text-align: center;
}